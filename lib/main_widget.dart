import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_portal/flutter_portal.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_keys.dart';
import 'package:saymee_flutter/core/constants/app_language.dart';
import 'package:saymee_flutter/core/constants/app_routes.dart';
import 'package:saymee_flutter/core/constants/app_stores.dart';
import 'package:saymee_flutter/core/constants/app_theme.dart';
import 'package:saymee_flutter/core/helpers/shared_preference_helper.dart';
import 'package:saymee_flutter/l10n/app_localizations.dart';
import 'package:saymee_flutter/modules/auth/general/auth_module_helper.dart';
import 'package:saymee_flutter/modules/auth/general/auth_module_routes.dart';
import 'package:saymee_flutter/modules/auth/presentation/blocs/auth_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MainWidget extends StatefulWidget {
  const MainWidget({super.key});

  @override
  State<MainWidget> createState() => _MainWidgetState();
}

class _MainWidgetState extends State<MainWidget> with WidgetsBindingObserver {
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();

    final sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();

    Modular.setNavigatorKey(AppKeys.navigatorKey);

    final isFirstOpenApp = sharedPreferenceHelper.get(
      key: AppStores.kFirstOpenApp,
    );

    Modular.setInitialRoute(
      '${AppRoutes.moduleAuth}${AuthModuleRoutes.signIn}',
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<AppLanguageBloc>(
          create: (context) =>
              Modular.get<AppLanguageBloc>()..add(InitLanguageEvent()),
        ),
        BlocProvider<AuthBloc>(create: (context) => Modular.get<AuthBloc>()),
      ],
      child: MediaQuery(
        data: MediaQuery.of(
          context,
        ).copyWith(textScaler: const TextScaler.linear(1)),
        child: Portal(
          child: BlocBuilder<AppLanguageBloc, AppLanguageState>(
            builder: (context, appLanguage) {
              return MaterialApp.router(
                title: 'Saymee',
                debugShowCheckedModeBanner: false,
                theme: ThemeData(
                  textSelectionTheme: TextSelectionThemeData(
                    cursorColor: AppColors.primary, // Cursor color
                    selectionColor: AppColors.primary.withValues(
                      alpha: 0.2,
                    ), // Background color when text is selected
                    selectionHandleColor: AppColors.primary, // Handle color (the draggable circles)
                  ),
                ),
                locale: appLanguage.locale,
                scaffoldMessengerKey: AppKeys.scaffoldMessengerKey,
                localizationsDelegates: const [
                  AppLocalizations.delegate,
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: AppLocalizations.supportedLocales,
                routerConfig: Modular.routerConfig,
              );
            },
          ),
        ),
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // get common data
    }
    super.didChangeAppLifecycleState(state);
  }
}
