import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee_flutter/core/constants/app_stores.dart';
import 'package:saymee_flutter/core/helpers/shared_preference_helper.dart';

enum AppLanguageType { vi, en }

final Map<AppLanguageType, Locale> _appLanguageLocales = {
  // en
  AppLanguageType.en: const Locale('en'),
  // vi
  AppLanguageType.vi: const Locale('vi'),
};

class AppLanguageState {
  final AppLanguageType languageType;

  AppLanguageState(this.languageType);

  Locale get locale => _appLanguageLocales[languageType]!;
}

abstract class AppLanguageEvent {}

class ChangeLanguageEvent extends AppLanguageEvent {
  final AppLanguageType languageType;

  ChangeLanguageEvent(this.languageType);
}

class InitLanguageEvent extends AppLanguageEvent {}

class AppLanguageBloc extends Bloc<AppLanguageEvent, AppLanguageState> {
  final SharedPreferenceHelper _sharedPreferenceHelper =
      Modular.get<SharedPreferenceHelper>();

  AppLanguageBloc() : super(AppLanguageState(AppLanguageType.en)) {
    on<InitLanguageEvent>((event, emit) {
      final savedLanguage =
          _sharedPreferenceHelper.get(key: AppStores.kAppLanguage) ??
          AppLanguageType.vi.name;
      final languageType = AppLanguageType.values.firstWhere(
        (e) => e.name == savedLanguage,
        orElse: () => AppLanguageType.vi,
      );
      emit(AppLanguageState(languageType));
    });
    on<ChangeLanguageEvent>((event, emit) {
      _sharedPreferenceHelper.set(
        key: AppStores.kAppLanguage,
        value: event.languageType.name,
      );
      emit(AppLanguageState(event.languageType));
    });
  }
}
