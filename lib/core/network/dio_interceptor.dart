import 'package:dio/dio.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:saymee_flutter/core/constants/app_stores.dart';
import 'package:saymee_flutter/core/helpers/general_helper.dart';
import 'package:saymee_flutter/core/helpers/shared_preference_helper.dart';
import 'package:saymee_flutter/core/utils/utils.dart';

class DioInterceptor extends Interceptor {
  final _sharedPreferenceHelper = Modular.get<SharedPreferenceHelper>();
  final Dio dio;

  DioInterceptor({required this.dio});

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    String? accessToken = _sharedPreferenceHelper.get(
      key: AppStores.kAccessToken,
    )?.toString();

    if (accessToken != null && accessToken.isNotEmpty) {
      options.headers['apisecret'] = accessToken;
    }

    options.headers['Device-Id'] = GeneralHelper.deviceId;
    options.headers['App-Version'] = GeneralHelper.appVersion;
    options.headers['OS-Info'] = GeneralHelper.osInfo;
    options.headers['Device-Info'] = GeneralHelper.deviceInfo;
    options.headers['OS-Version'] = GeneralHelper.osVersion;

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    Utils.debugLog(err.requestOptions.path);

    return handler.reject(err);
  }
}
