import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:saymee_flutter/core/components/buttons/primary_button.dart';
import 'package:saymee_flutter/core/components/buttons/secondary_button.dart';

class AppOtpDialog {
  static void show({
    required BuildContext context,
    int length = 4,
    String? title,
    String? message,
    required Function(String) onConfirm,
    VoidCallback? onCancel,
    String? errorMessage,
    VoidCallback? onResendPress,
    bool barrierDismissible = false,
  }) {
    showDialog(
      barrierDismissible: barrierDismissible,
      context: context,
      builder: (context) {
        return PopScope(
          canPop: barrierDismissible,
          child: _OtpDialogWidget(
            length: length,
            title: title,
            message: message,
            onConfirm: onConfirm,
            onCancel: onCancel,
            errorMessage: errorMessage,
            onResendPress: onResendPress,
          ),
        );
      },
    );
  }
}

class _OtpDialogWidget extends StatefulWidget {
  final int length;
  final String? title;
  final String? message;
  final Function(String) onConfirm;
  final VoidCallback? onCancel;
  final String? errorMessage;
  final VoidCallback? onResendPress;

  const _OtpDialogWidget({
    required this.length,
    this.title,
    this.message,
    required this.onConfirm,
    this.onCancel,
    this.errorMessage,
    this.onResendPress,
  });

  @override
  State<_OtpDialogWidget> createState() => _OtpDialogWidgetState();
}

class _OtpDialogWidgetState extends State<_OtpDialogWidget> {
  late List<TextEditingController> _controllers;
  late List<FocusNode> _focusNodes;
  String _currentError = '';

  @override
  void initState() {
    super.initState();
    _controllers = List.generate(widget.length, (index) => TextEditingController());
    _focusNodes = List.generate(widget.length, (index) => FocusNode());

    // Set error message if provided
    _currentError = widget.errorMessage ?? '';
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _onChanged(String value, int index) {
    setState(() {
      _currentError = ''; // Clear error when user types
    });

    if (value.isNotEmpty) {
      // Move to next field
      if (index < widget.length - 1) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last field, check if OTP is complete
        _checkOtpComplete();
      }
    }
  }

  void _onBackspace(int index) {
    if (index > 0 && _controllers[index].text.isEmpty) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  void _checkOtpComplete() {
    String otp = _controllers.map((controller) => controller.text).join();
    if (otp.length == widget.length) {
      widget.onConfirm(otp);
    }
  }

  void _clearOtp() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
    setState(() {
      _currentError = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Title
            if (widget.title != null) ...[
              Text(
                widget.title!,
                style: Styles.h5.smb.copyWith(color: Colors.black),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
            ],

            // Message
            if (widget.message != null) ...[
              Text(
                widget.message!,
                style: Styles.medium.regular.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
            ],

            // OTP Input Fields
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: List.generate(
                widget.length,
                (index) => _buildOtpField(index),
              ),
            ),

            // Error Message
            if (_currentError.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                _currentError,
                style: Styles.small.regular.copyWith(color: Colors.red),
                textAlign: TextAlign.center,
              ),
            ],

            const SizedBox(height: 24),

            // Buttons
            Row(
              children: [
                if (widget.onCancel != null) ...[
                  Expanded(
                    child: SecondaryButton(
                      text: 'Cancel',
                      onPress: widget.onCancel!,
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: PrimaryButton(
                    text: 'Confirm',
                    onPress: () {
                      String otp = _controllers.map((c) => c.text).join();
                      if (otp.length == widget.length) {
                        widget.onConfirm(otp);
                      } else {
                        setState(() {
                          _currentError = 'Please enter complete OTP';
                        });
                      }
                    },
                  ),
                ),
              ],
            ),

            // Resend Button
            if (widget.onResendPress != null) ...[
              const SizedBox(height: 16),
              TextButton(
                onPressed: widget.onResendPress,
                child: Text(
                  'Resend OTP',
                  style: Styles.medium.regular.copyWith(
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOtpField(int index) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        border: Border.all(
          color: _currentError.isNotEmpty
            ? Colors.red
            : (_focusNodes[index].hasFocus ? Theme.of(context).primaryColor : Colors.grey[300]!),
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _controllers[index],
        focusNode: _focusNodes[index],
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        maxLength: 1,
        style: Styles.large.smb,
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        decoration: const InputDecoration(
          border: InputBorder.none,
          counterText: '',
        ),
        onChanged: (value) => _onChanged(value, index),
        onTap: () {
          // Clear error when user taps on field
          if (_currentError.isNotEmpty) {
            setState(() {
              _currentError = '';
            });
          }
        },
        onFieldSubmitted: (value) {
          if (value.isEmpty && index > 0) {
            _onBackspace(index);
          }
        },
      ),
    );
  }
}
