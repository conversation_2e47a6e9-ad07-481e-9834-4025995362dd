import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:saymee_flutter/core/constants/app_colors.dart';
import 'package:saymee_flutter/core/constants/app_icons.dart';
import 'package:saymee_flutter/core/constants/app_styles.dart';
import 'package:saymee_flutter/core/extensions/widget_extension.dart';
import 'package:saymee_flutter/core/utils/utils.dart';

class TextInput extends StatefulWidget {
  // --- props: ---
  final TextInputType? keyboardType;
  final String? initialText;
  final String? placeholder;
  final void Function(String)? onChange;
  final bool Function(String)? validator;
  final String? errorMessage;
  final TextStyle? textStyle;
  final TextStyle? labelStyle;
  final TextEditingController? controller;
  final bool disabled;
  final bool multiline;
  final GlobalKey<FormState>? formKey;

  // --- constructor: ---
  const TextInput({
    super.key,
    this.keyboardType,
    this.initialText,
    this.placeholder,
    this.onChange,
    this.validator,
    this.errorMessage,
    this.textStyle,
    this.controller,
    this.disabled = false,
    this.multiline = false,
    this.formKey,
    this.labelStyle,
  });

  @override
  State<TextInput> createState() => _TextInputState();
}

class _TextInputState extends State<TextInput> {
  late FocusNode _focus;
  bool _isFocused = false;
  bool _showClearIcon = false;
  String _errorMessage = '';

  // --- methods ---
  void _setFocusState() {
    if (_focus.hasFocus == false) {
      setState(() {
        _isFocused = false;
      });
    } else {
      setState(() {
        _isFocused = true;
      });
    }
  }

  // show or hide suffix icon when focus or blur input or when input is empty
  void _setShowClearIcon() {
    if (_focus.hasFocus == false || widget.controller?.text == "") {
      setState(() {
        _showClearIcon = false;
      });
    } else {
      setState(() {
        _showClearIcon = true;
      });
    }
  }

  @override
  void initState() {
    _focus = FocusNode();
    _focus.addListener(() {
      if (mounted) {
        _setFocusState();
        _setShowClearIcon();
      }

      if (widget.initialText != null) {
        widget.controller?.text = widget.initialText!;
      }
    });

    widget.controller?.addListener(() {
      if (mounted) {
        _setShowClearIcon();
        _setFocusState();
      }
      // _onSubmit();
    });

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _clearInput() {
    widget.controller?.clear();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 44,
          alignment: Alignment.center,
          padding: EdgeInsets.only(left: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: widget.disabled
                ? null
                : (Border.all(
                    color: _errorMessage.isNotEmpty
                        ? AppColors.danger
                        : (_isFocused ? AppColors.primary : Colors.transparent),
                    width: 1,
                  )),
          ),
          child: TextFormField(
            enabled: !widget.disabled,
            controller: widget.controller,
            maxLines: widget.multiline ? null : 1,
            minLines: 1,
            textAlignVertical: TextAlignVertical.center,
            style: widget.textStyle ?? Styles.medium.regular,
            onTapOutside: (event) => Utils.hideKeyboard(),
            cursorColor: AppColors.primary,
            focusNode: _focus,
            keyboardType: widget.keyboardType,
            onChanged: (value) {
              widget.formKey?.currentState?.validate();
              widget.validator?.call(value);
            },
            validator: (value) {
              bool isError = widget.validator?.call(value ?? '') ?? false;
              String errorMessage = isError ? widget.errorMessage ?? "" : "";
              setState(() {
                _errorMessage = errorMessage;
              });

              return errorMessage;
            },
            // --- style of input ---
            decoration: InputDecoration(
              hintText: widget.placeholder,
              hintStyle: Styles.medium.secondary,
              // errorStyle: const TextStyle(height: 0.01, fontSize: 0),
              border: InputBorder.none,
              isDense: true,
              suffixIcon: _showClearIcon
                  ? SizedBox(
                      height: 44,
                      width: 44,
                      child: IconButton(
                        iconSize: 20,
                        padding: EdgeInsets.zero,
                        splashColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        icon: SvgPicture.asset(
                          AppIcons.iconClose,
                          width: 20,
                          height: 20,
                          colorFilter: ColorFilter.mode(
                            Colors.black,
                            BlendMode.srcIn,
                          ),
                        ),
                        onPressed: _clearInput,
                      ),
                    )
                  : null,
            ),
          ),
        ),
        Container(
          child: _errorMessage.isNotEmpty
              ? Text(
                  _errorMessage,
                  style: const TextStyle(fontSize: 11, color: AppColors.danger),
                ).paddingOnly(top: 2)
              : const SizedBox.shrink(),
        ),
      ],
    );
  }
}
