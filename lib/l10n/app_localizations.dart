import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_vi.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('vi'),
  ];

  /// No description provided for @emailCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'Email cannot be empty'**
  String get emailCannotEmpty;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Invalid email'**
  String get invalidEmail;

  /// No description provided for @passwordCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'Password cannot be empty!'**
  String get passwordCannotEmpty;

  /// No description provided for @passwordShorter6Character.
  ///
  /// In en, this message translates to:
  /// **'Password cannot be shorter than 6 characters!'**
  String get passwordShorter6Character;

  /// No description provided for @passwordError.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match, please enter again!'**
  String get passwordError;

  /// No description provided for @phoneNumberCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'Phone number cannot be empty'**
  String get phoneNumberCannotEmpty;

  /// No description provided for @invalidPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number'**
  String get invalidPhoneNumber;

  /// No description provided for @pleaseTryAgain.
  ///
  /// In en, this message translates to:
  /// **'An error occurred, please try again.'**
  String get pleaseTryAgain;

  /// No description provided for @sessionHasExpired.
  ///
  /// In en, this message translates to:
  /// **'Session has expired!'**
  String get sessionHasExpired;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @notification.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// No description provided for @signing.
  ///
  /// In en, this message translates to:
  /// **'Signing'**
  String get signing;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'Sign in'**
  String get signIn;

  /// No description provided for @enterEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter email'**
  String get enterEmail;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @enterPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter password'**
  String get enterPassword;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot password'**
  String get forgotPassword;

  /// No description provided for @termsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Terms and conditions'**
  String get termsAndConditions;

  /// No description provided for @signOut.
  ///
  /// In en, this message translates to:
  /// **'Sign out'**
  String get signOut;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @confirmSignOut.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out of this account?'**
  String get confirmSignOut;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @fullNameCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'Full name cannot be empty'**
  String get fullNameCannotEmpty;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm password'**
  String get confirmPassword;

  /// No description provided for @goodMorning.
  ///
  /// In en, this message translates to:
  /// **'Good morning'**
  String get goodMorning;

  /// No description provided for @goodAfternoon.
  ///
  /// In en, this message translates to:
  /// **'Good afternoon'**
  String get goodAfternoon;

  /// No description provided for @goodEvening.
  ///
  /// In en, this message translates to:
  /// **'Good evening'**
  String get goodEvening;

  /// No description provided for @goodNight.
  ///
  /// In en, this message translates to:
  /// **'Good night'**
  String get goodNight;

  /// No description provided for @usernameCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'Username is cannot empty!'**
  String get usernameCannotEmpty;

  /// No description provided for @usernameInvalid.
  ///
  /// In en, this message translates to:
  /// **'Username invalid!'**
  String get usernameInvalid;

  /// No description provided for @otpCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'OTP cannot empty!'**
  String get otpCannotEmpty;

  /// No description provided for @groupNameCannotEmpty.
  ///
  /// In en, this message translates to:
  /// **'Group name cannot empty!'**
  String get groupNameCannotEmpty;

  /// No description provided for @confirmCreateRoom.
  ///
  /// In en, this message translates to:
  /// **'You already have a chat session with {name}. Would you like to continue existing session or start a new one ?'**
  String confirmCreateRoom(String name);

  /// No description provided for @continueTitle.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueTitle;

  /// No description provided for @newSession.
  ///
  /// In en, this message translates to:
  /// **'New Session'**
  String get newSession;

  /// No description provided for @chatbotList.
  ///
  /// In en, this message translates to:
  /// **'Chatbot List'**
  String get chatbotList;

  /// No description provided for @onboardingContent1.
  ///
  /// In en, this message translates to:
  /// **'Group Chat with AI Chatbot'**
  String get onboardingContent1;

  /// No description provided for @onboardingContent2.
  ///
  /// In en, this message translates to:
  /// **'Effective advice in relationships'**
  String get onboardingContent2;

  /// No description provided for @onboardingContent3.
  ///
  /// In en, this message translates to:
  /// **'Ask ANYTHING with your friends'**
  String get onboardingContent3;

  /// No description provided for @recent.
  ///
  /// In en, this message translates to:
  /// **'Recent'**
  String get recent;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @chat.
  ///
  /// In en, this message translates to:
  /// **'Chat'**
  String get chat;

  /// No description provided for @room.
  ///
  /// In en, this message translates to:
  /// **'Room'**
  String get room;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @activateAccountSuccess.
  ///
  /// In en, this message translates to:
  /// **'Activate account success'**
  String get activateAccountSuccess;

  /// No description provided for @activating.
  ///
  /// In en, this message translates to:
  /// **'Activating'**
  String get activating;

  /// No description provided for @activateAccount.
  ///
  /// In en, this message translates to:
  /// **'Activate account'**
  String get activateAccount;

  /// No description provided for @otpSentToEmail.
  ///
  /// In en, this message translates to:
  /// **'An OTP has been sent to the email '**
  String get otpSentToEmail;

  /// No description provided for @otpToActivateAccount.
  ///
  /// In en, this message translates to:
  /// **'. Please use that OTP to activate your account.'**
  String get otpToActivateAccount;

  /// No description provided for @otpCode.
  ///
  /// In en, this message translates to:
  /// **'OTP Code'**
  String get otpCode;

  /// No description provided for @inputOtpCode.
  ///
  /// In en, this message translates to:
  /// **'Input OTP Code'**
  String get inputOtpCode;

  /// No description provided for @enterEmailForResetPassword.
  ///
  /// In en, this message translates to:
  /// **'Enter your email address to receive the code and instructions to reset your password.'**
  String get enterEmailForResetPassword;

  /// No description provided for @inputYourEmail.
  ///
  /// In en, this message translates to:
  /// **'Input your email'**
  String get inputYourEmail;

  /// No description provided for @signUp.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUp;

  /// No description provided for @signUpWithEmail.
  ///
  /// In en, this message translates to:
  /// **'Sign up with Email'**
  String get signUpWithEmail;

  /// No description provided for @fullname.
  ///
  /// In en, this message translates to:
  /// **'Full name'**
  String get fullname;

  /// No description provided for @inputYourFullname.
  ///
  /// In en, this message translates to:
  /// **'Input your full name'**
  String get inputYourFullname;

  /// No description provided for @username.
  ///
  /// In en, this message translates to:
  /// **'Username'**
  String get username;

  /// No description provided for @chooseAnUsername.
  ///
  /// In en, this message translates to:
  /// **'Choose an username'**
  String get chooseAnUsername;

  /// No description provided for @registering.
  ///
  /// In en, this message translates to:
  /// **'Registering'**
  String get registering;

  /// No description provided for @resetPasswordSuccess.
  ///
  /// In en, this message translates to:
  /// **'Reset password success'**
  String get resetPasswordSuccess;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset password'**
  String get resetPassword;

  /// No description provided for @otpToResetPassword.
  ///
  /// In en, this message translates to:
  /// **'. Please use that OTP to reset password.'**
  String get otpToResetPassword;

  /// No description provided for @signInWithEmail.
  ///
  /// In en, this message translates to:
  /// **'Sign in with Email'**
  String get signInWithEmail;

  /// No description provided for @signInWithApple.
  ///
  /// In en, this message translates to:
  /// **'Sign in with Apple'**
  String get signInWithApple;

  /// No description provided for @notAccount.
  ///
  /// In en, this message translates to:
  /// **'You don’t have an account?'**
  String get notAccount;

  /// No description provided for @signUpHere.
  ///
  /// In en, this message translates to:
  /// **' Sign up here'**
  String get signUpHere;

  /// No description provided for @byUsingService.
  ///
  /// In en, this message translates to:
  /// **'By using our services you are agreeing to our '**
  String get byUsingService;

  /// No description provided for @terms.
  ///
  /// In en, this message translates to:
  /// **'Terms'**
  String get terms;

  /// No description provided for @termAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Term & Conditions'**
  String get termAndConditions;

  /// No description provided for @and.
  ///
  /// In en, this message translates to:
  /// **' and '**
  String get and;

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'Or'**
  String get or;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @blockMember.
  ///
  /// In en, this message translates to:
  /// **'Block a member'**
  String get blockMember;

  /// No description provided for @removeFromGroup.
  ///
  /// In en, this message translates to:
  /// **'Remove from group'**
  String get removeFromGroup;

  /// No description provided for @unblockMember.
  ///
  /// In en, this message translates to:
  /// **'Unblock a member'**
  String get unblockMember;

  /// No description provided for @selectChatbot.
  ///
  /// In en, this message translates to:
  /// **'Select Chatbot'**
  String get selectChatbot;

  /// No description provided for @groupIntro.
  ///
  /// In en, this message translates to:
  /// **'Group Introduction'**
  String get groupIntro;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @writeShortDescription.
  ///
  /// In en, this message translates to:
  /// **'Write a short descriptions'**
  String get writeShortDescription;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @findGroupsToJoin.
  ///
  /// In en, this message translates to:
  /// **'Find groups to join'**
  String get findGroupsToJoin;

  /// No description provided for @yourPersonalFriend.
  ///
  /// In en, this message translates to:
  /// **'Your Personal A.I Companion'**
  String get yourPersonalFriend;

  /// No description provided for @yourPersonalFriendContent.
  ///
  /// In en, this message translates to:
  /// **'Engages in natural and meaningful conversations with humor, empathy, and creativity.'**
  String get yourPersonalFriendContent;

  /// No description provided for @startConversation.
  ///
  /// In en, this message translates to:
  /// **'Start a conversation'**
  String get startConversation;

  /// No description provided for @chats.
  ///
  /// In en, this message translates to:
  /// **'Chats'**
  String get chats;

  /// No description provided for @openSetting.
  ///
  /// In en, this message translates to:
  /// **'Open setting'**
  String get openSetting;

  /// No description provided for @noPermission.
  ///
  /// In en, this message translates to:
  /// **'No permission'**
  String get noPermission;

  /// No description provided for @pleaseOpenSetting.
  ///
  /// In en, this message translates to:
  /// **'Please open settings to grant access to camera'**
  String get pleaseOpenSetting;

  /// No description provided for @howCanIHelpYou.
  ///
  /// In en, this message translates to:
  /// **'Hello, How can i help you?'**
  String get howCanIHelpYou;

  /// No description provided for @addMemberToCreateGroup.
  ///
  /// In en, this message translates to:
  /// **'Add member to create group'**
  String get addMemberToCreateGroup;

  /// No description provided for @clearContext.
  ///
  /// In en, this message translates to:
  /// **'Clear context'**
  String get clearContext;

  /// No description provided for @clearMessage.
  ///
  /// In en, this message translates to:
  /// **'Clear Message'**
  String get clearMessage;

  /// No description provided for @clearMessageContent.
  ///
  /// In en, this message translates to:
  /// **'Clearing message data is permanent, the messages cannot be recovered.'**
  String get clearMessageContent;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @confirmDelete.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete?'**
  String get confirmDelete;

  /// No description provided for @members.
  ///
  /// In en, this message translates to:
  /// **'members'**
  String get members;

  /// No description provided for @talkChatbot.
  ///
  /// In en, this message translates to:
  /// **'Talk Chatbot'**
  String get talkChatbot;

  /// No description provided for @creatingRoom.
  ///
  /// In en, this message translates to:
  /// **'Creating room'**
  String get creatingRoom;

  /// No description provided for @createGroup.
  ///
  /// In en, this message translates to:
  /// **'Create Group'**
  String get createGroup;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @nameGroupChat.
  ///
  /// In en, this message translates to:
  /// **'Name group chat'**
  String get nameGroupChat;

  /// No description provided for @inputNameGroupChat.
  ///
  /// In en, this message translates to:
  /// **'Input name group chat'**
  String get inputNameGroupChat;

  /// No description provided for @uploadImage.
  ///
  /// In en, this message translates to:
  /// **'Upload image'**
  String get uploadImage;

  /// No description provided for @groupMembers.
  ///
  /// In en, this message translates to:
  /// **'Group Members'**
  String get groupMembers;

  /// No description provided for @updateSuccess.
  ///
  /// In en, this message translates to:
  /// **'Update success!'**
  String get updateSuccess;

  /// No description provided for @clearSuccess.
  ///
  /// In en, this message translates to:
  /// **'Clear success!'**
  String get clearSuccess;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @mute.
  ///
  /// In en, this message translates to:
  /// **'Mute'**
  String get mute;

  /// No description provided for @unmute.
  ///
  /// In en, this message translates to:
  /// **'Unmute'**
  String get unmute;

  /// No description provided for @leave.
  ///
  /// In en, this message translates to:
  /// **'Leave'**
  String get leave;

  /// No description provided for @leaveGroup.
  ///
  /// In en, this message translates to:
  /// **'Leave Group'**
  String get leaveGroup;

  /// No description provided for @leaveGroupContent.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to leave the group?'**
  String get leaveGroupContent;

  /// No description provided for @groupInfo.
  ///
  /// In en, this message translates to:
  /// **'Group info'**
  String get groupInfo;

  /// No description provided for @inviteLink.
  ///
  /// In en, this message translates to:
  /// **'Invite Link'**
  String get inviteLink;

  /// No description provided for @copyLink.
  ///
  /// In en, this message translates to:
  /// **'Copy Link'**
  String get copyLink;

  /// No description provided for @copied.
  ///
  /// In en, this message translates to:
  /// **'Copied'**
  String get copied;

  /// No description provided for @writeDescription.
  ///
  /// In en, this message translates to:
  /// **'Write a short description and add photo of the group chat to introduce it to new users.'**
  String get writeDescription;

  /// No description provided for @nameOrAvatarGroup.
  ///
  /// In en, this message translates to:
  /// **'Name or avatar group'**
  String get nameOrAvatarGroup;

  /// No description provided for @updateNameOrAvatarGroup.
  ///
  /// In en, this message translates to:
  /// **'Updating the avatar and name makes the group easier to find and recognize.'**
  String get updateNameOrAvatarGroup;

  /// No description provided for @setGroupPrivate.
  ///
  /// In en, this message translates to:
  /// **'Set group as private'**
  String get setGroupPrivate;

  /// No description provided for @setGroupPrivateContent.
  ///
  /// In en, this message translates to:
  /// **'The content of private group chats is not searchable within public channels, ensuring privacy for those conversations.'**
  String get setGroupPrivateContent;

  /// No description provided for @memberRequest.
  ///
  /// In en, this message translates to:
  /// **'Member requests'**
  String get memberRequest;

  /// No description provided for @memberRequestContent.
  ///
  /// In en, this message translates to:
  /// **'Admins can approve requests to join group'**
  String get memberRequestContent;

  /// No description provided for @groupManage.
  ///
  /// In en, this message translates to:
  /// **'Group manage'**
  String get groupManage;

  /// No description provided for @deleteGroup.
  ///
  /// In en, this message translates to:
  /// **'Delete group'**
  String get deleteGroup;

  /// No description provided for @deleteGroupContent.
  ///
  /// In en, this message translates to:
  /// **'Deleting a group is permanent, all content will be irretrievably lost.'**
  String get deleteGroupContent;

  /// No description provided for @confirmDeleteGroup.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete group?'**
  String get confirmDeleteGroup;

  /// No description provided for @groupSetting.
  ///
  /// In en, this message translates to:
  /// **'Group Settings'**
  String get groupSetting;

  /// No description provided for @requesting.
  ///
  /// In en, this message translates to:
  /// **'Requesting'**
  String get requesting;

  /// No description provided for @joining.
  ///
  /// In en, this message translates to:
  /// **'Joining'**
  String get joining;

  /// No description provided for @rejecting.
  ///
  /// In en, this message translates to:
  /// **'Rejecting'**
  String get rejecting;

  /// No description provided for @deny.
  ///
  /// In en, this message translates to:
  /// **'Deny'**
  String get deny;

  /// No description provided for @accept.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get accept;

  /// No description provided for @requested.
  ///
  /// In en, this message translates to:
  /// **'Requested'**
  String get requested;

  /// No description provided for @joinTheGroup.
  ///
  /// In en, this message translates to:
  /// **'Join the group'**
  String get joinTheGroup;

  /// No description provided for @groupInvitation.
  ///
  /// In en, this message translates to:
  /// **'Group invitation'**
  String get groupInvitation;

  /// No description provided for @imageDownloadedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Image downloaded successfully!'**
  String get imageDownloadedSuccess;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @roomIntro.
  ///
  /// In en, this message translates to:
  /// **'Experience the ultimate Group Chat with A.I'**
  String get roomIntro;

  /// No description provided for @roomIntroContent.
  ///
  /// In en, this message translates to:
  /// **'Invite your friends to join the chat and chat with the AI as a group.'**
  String get roomIntroContent;

  /// No description provided for @createNewGroup.
  ///
  /// In en, this message translates to:
  /// **'Create a new group'**
  String get createNewGroup;

  /// No description provided for @chatRoom.
  ///
  /// In en, this message translates to:
  /// **'Chat Room'**
  String get chatRoom;

  /// No description provided for @updating.
  ///
  /// In en, this message translates to:
  /// **'Updating'**
  String get updating;

  /// No description provided for @credits.
  ///
  /// In en, this message translates to:
  /// **'credits'**
  String get credits;

  /// No description provided for @forTitle.
  ///
  /// In en, this message translates to:
  /// **'for'**
  String get forTitle;

  /// No description provided for @months.
  ///
  /// In en, this message translates to:
  /// **'months'**
  String get months;

  /// No description provided for @paymentSuccess.
  ///
  /// In en, this message translates to:
  /// **'Payment success'**
  String get paymentSuccess;

  /// No description provided for @paymentFailed.
  ///
  /// In en, this message translates to:
  /// **'Payment failed'**
  String get paymentFailed;

  /// No description provided for @upgradeTo.
  ///
  /// In en, this message translates to:
  /// **'Upgrade to '**
  String get upgradeTo;

  /// No description provided for @premium.
  ///
  /// In en, this message translates to:
  /// **'premium'**
  String get premium;

  /// No description provided for @plan.
  ///
  /// In en, this message translates to:
  /// **' plan'**
  String get plan;

  /// No description provided for @andEnjoyAccess.
  ///
  /// In en, this message translates to:
  /// **'And enjoy access to all features of MindMate '**
  String get andEnjoyAccess;

  /// No description provided for @noAds.
  ///
  /// In en, this message translates to:
  /// **'No ads'**
  String get noAds;

  /// No description provided for @accessModel.
  ///
  /// In en, this message translates to:
  /// **'Access to the smartest AI model'**
  String get accessModel;

  /// No description provided for @fasterResponse.
  ///
  /// In en, this message translates to:
  /// **'Faster response times'**
  String get fasterResponse;

  /// No description provided for @restorePurchases.
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get restorePurchases;

  /// No description provided for @subContent1.
  ///
  /// In en, this message translates to:
  /// **'Subscribed user has unlimited access to the services.'**
  String get subContent1;

  /// No description provided for @subContent2.
  ///
  /// In en, this message translates to:
  /// **'Payment will be charged to iTunes Account at purchase confirmation'**
  String get subContent2;

  /// No description provided for @subContent3.
  ///
  /// In en, this message translates to:
  /// **'Subscription automatically renews within 24-hours prior to the end of the current subscription period'**
  String get subContent3;

  /// No description provided for @subContent4.
  ///
  /// In en, this message translates to:
  /// **'Subscription may be managed and auto-renewal may be turned off by going to the Users Account Settings after purchased'**
  String get subContent4;

  /// No description provided for @subContent5.
  ///
  /// In en, this message translates to:
  /// **'Any unused portion of a free trial period, if offered,will be forfeited when user purchases a subscription to that publication, where applicable'**
  String get subContent5;

  /// No description provided for @expiryDate.
  ///
  /// In en, this message translates to:
  /// **'Expiry date'**
  String get expiryDate;

  /// No description provided for @expired.
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get expired;

  /// No description provided for @creditsTitle.
  ///
  /// In en, this message translates to:
  /// **'Credits'**
  String get creditsTitle;

  /// No description provided for @upgradeToPremium.
  ///
  /// In en, this message translates to:
  /// **'Upgrade to premium'**
  String get upgradeToPremium;

  /// No description provided for @subscribeNow.
  ///
  /// In en, this message translates to:
  /// **'Subscribe Now'**
  String get subscribeNow;

  /// No description provided for @accountInfo.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get accountInfo;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @setNewPhoto.
  ///
  /// In en, this message translates to:
  /// **'Set New Photo'**
  String get setNewPhoto;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get logout;

  /// No description provided for @confirmLogout.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get confirmLogout;

  /// No description provided for @appConfiguration.
  ///
  /// In en, this message translates to:
  /// **'App configuration'**
  String get appConfiguration;

  /// No description provided for @store.
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get store;

  /// No description provided for @enhanceYourExperience.
  ///
  /// In en, this message translates to:
  /// **'Enhance your experience'**
  String get enhanceYourExperience;

  /// No description provided for @rateApp.
  ///
  /// In en, this message translates to:
  /// **'Rate app'**
  String get rateApp;

  /// No description provided for @shareWithFriend.
  ///
  /// In en, this message translates to:
  /// **'Share with friend'**
  String get shareWithFriend;

  /// No description provided for @helpAndSupport.
  ///
  /// In en, this message translates to:
  /// **'Help and support'**
  String get helpAndSupport;

  /// No description provided for @faq.
  ///
  /// In en, this message translates to:
  /// **'FAQs'**
  String get faq;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact us'**
  String get contactUs;

  /// No description provided for @copyRight.
  ///
  /// In en, this message translates to:
  /// **'Copyright ©2024 by MDC'**
  String get copyRight;

  /// No description provided for @later.
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get later;

  /// No description provided for @upgrade.
  ///
  /// In en, this message translates to:
  /// **'Upgrade'**
  String get upgrade;

  /// No description provided for @hi_saymee.
  ///
  /// In en, this message translates to:
  /// **'Hello Saymee here!!'**
  String get hi_saymee;

  /// No description provided for @placeholder_phone.
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number'**
  String get placeholder_phone;

  /// No description provided for @label_phone.
  ///
  /// In en, this message translates to:
  /// **'Phone'**
  String get label_phone;

  /// No description provided for @login_otp.
  ///
  /// In en, this message translates to:
  /// **'Login with OTP'**
  String get login_otp;

  /// No description provided for @try_saymee.
  ///
  /// In en, this message translates to:
  /// **'Try Saymee'**
  String get try_saymee;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'vi'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'vi':
      return AppLocalizationsVi();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
